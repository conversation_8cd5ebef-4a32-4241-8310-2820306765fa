apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: ai-spring-backend-sample-production
  namespace: argocd
  labels:
    app.kubernetes.io/name: ai-spring-backend-sample-production
    app.kubernetes.io/part-of: ai-spring-backend-sample
    app.kubernetes.io/component: springboot-backend
    app.kubernetes.io/version: b4c7f09b
    app.kubernetes.io/managed-by: argocd
    environment: production
    app-type: springboot-backend
    source.repo: ChidhagniConsulting-ai-spring-backend
    source.branch: 25-merge
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: ai-spring-backend-sample-project
  source:
    repoURL: https://github.com/ChidhagniConsulting/gitops-argocd-apps
    targetRevision: main
    path: deployments/ai-spring-backend-sample/overlays/production
  destination:
    server: https://6be4e15d-52f9-431d-84ec-ec8cad0dff2d.k8s.ondigitalocean.com
    namespace: ai-spring-backend-sample-production
  syncPolicy:
    automated:
      prune: false
      selfHeal: false
      allowEmpty: false
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
    retry:
      limit: 3
      backoff:
        duration: 10s
        factor: 2
        maxDuration: 5m
  revisionHistoryLimit: 20
  ignoreDifferences:
  - group: apps
    kind: Deployment
    jsonPointers:
    - /spec/replicas
  info:
  - name: Description
    value: "Production environment for ai-spring-backend-sample"
  - name: Repository
    value: "https://github.com/ChidhagniConsulting-ai-spring-backend"
  - name: Environment
    value: "production"
  - name: Application Type
    value: "springboot-backend"
  - name: Source Branch
    value: "25-merge"
  - name: Commit SHA
    value: "b4c7f09b"
  - name: Configuration
    value: "Production configuration with high availability and security"
