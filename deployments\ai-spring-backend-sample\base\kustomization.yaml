apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: ai-spring-backend-sample

resources:
- deployment.yaml
- service.yaml
- configmap.yaml
- secret.yaml

labels:
- pairs:
    app: ai-spring-backend-sample
    app.kubernetes.io/name: ai-spring-backend-sample
    app.kubernetes.io/part-of: ai-spring-backend-sample
    app.kubernetes.io/managed-by: argocd
    source.repo: ChidhagniConsulting-ai-spring-backend
    source.branch: 25-merge

commonAnnotations:
  app.kubernetes.io/managed-by: kustomize
  source.commit: b4c7f09b

namePrefix: ""
nameSuffix: ""

images:
- name: registry.digitalocean.com/doks-registry/ai-spring-backend:production
  newName: registry.digitalocean.com/doks-registry/ai-spring-backend:production

replicas:
- name: ai-spring-backend-sample
  count: 1
