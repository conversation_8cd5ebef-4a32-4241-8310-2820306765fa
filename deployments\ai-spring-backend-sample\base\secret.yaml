apiVersion: v1
kind: Secret
metadata:
  name: ai-spring-backend-sample-secrets
  labels:
    app: ai-spring-backend-sample
    app.kubernetes.io/name: ai-spring-backend-sample
    app.kubernetes.io/component: secrets
    app.kubernetes.io/part-of: ai-spring-backend-sample
    app.kubernetes.io/version: b4c7f09b
    app.kubernetes.io/managed-by: argocd
type: Opaque
data:
  # Dynamic secrets extracted from GitHub Actions secrets JSON payload
  # These placeholders will be replaced with base64-encoded values from secrets_encoded

  # Essential Authentication Secrets
  JWT_SECRET: dmVyeWxvbmdjaGlkaGdhbmlsb2NhbHNlY3JldHlvdXdpbGxub3R1bmRlcnN0YW5kZGFoc2RzamFsa2Rqc2FkbnNhZHNhbGtkanNhbGtkaGFjeWRzYWhmZGxrZmpkc2xrZmpkc2Zwb2RzaWZwZHN5Y3hpb3Zsa2N4dmZkZmpkc2hmaXVzeWZ1eXJlcmV3anJld2Zkc2Fkc2Fkc2Fkc2Fkc2FkYXNkc2Fkc2Fkc2FkY3h2Y3h2ZGY=

  # Database Credentials
  DB_USER: c3ByaW5nX2Rldl91c2Vy
  DB_PASSWORD: QVZOU18wYll6dDBHWmRreTdyblA4S2w3
  DB_HOST: cHJpdmF0ZS1kYmFhcy1kYi0xMDMyOTMyOC1kby11c2VyLTIzODE1NzQyLTAubS5kYi5vbmRpZ2l0YWxvY2Vhbi5jb20=
  DB_PORT: MjUwNjA=
  DB_NAME: c3ByaW5nX2Rldl9kYg==

  # SMTP Configuration
  SMTP_USER: ****************************************
  SMTP_PASS: ZnFhY3RlaGFmbXpsbHR6eg==

  # OAuth2 Configuration
  GOOGLE_CLIENT_ID: MTA3Mzk4MTg2NDUzOC0zdWlpazcyb2hzZnIyb3Vpb3JvcjNmbTFqcWM0OTNvcy5hcHBzLmdvb2dsZXVzZXJjb250ZW50LmNvbQ==
  GOOGLE_CLIENT_SECRET: R09DU1BYLTcyRjBONEg5aGlMSVk1U3o1Z3pCczI5OEFBYlQ=

  # Database SSL Mode (fixed value)
  DB_SSL_MODE: cmVxdWlyZQ==
